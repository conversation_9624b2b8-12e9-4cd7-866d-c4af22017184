import OGDIService

/// A shared container class for badge dependencies.
public final class OGBadgeContainer: OGDISharedContainer {
  private static let _shared: OGBadgeContainer = .init()
  public static var shared: OGBadgeContainer {
    get { _shared }
    set {
      preconditionFailure("OGRoutingContainer should not be set")
    }
  }

  private let _manager: OGDIContainerManager = .init()
  public var manager: OGDIContainerManager {
    get { _manager }
    set {
      preconditionFailure("OGDIContainerManager should not be set")
    }
  }

  /// The service for badge publishing.
  public var badgePublisher: OGDIService<
    any OGBadgePublishing
  > {
    self {
      OGBadgePublisher()
    }.cached
  }

  public var badgeStore: OGDIService<
    OGBadgeStore
  > {
    self {
      OGBadgeStore.make()
    }.shared
  }
}
