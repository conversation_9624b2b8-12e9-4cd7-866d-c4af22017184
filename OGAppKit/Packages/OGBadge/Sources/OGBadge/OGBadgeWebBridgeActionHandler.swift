import OGDIService
import OGNavigationCore
import OGWebBridge

/// A class conforming to OGWebBridgeActionHandlable, responsible for receiving badge updates.
public struct OGBadgeWebBridgeActionHandler: OGWebBridgeActionHandlable {
  @OGInjected(\OGBadgeContainer.badgePublisher) private var badgePublisher
  let tabs: [OGTab]

  /// Initializes the `OGBadgeWebBridgeActionHandler` with the specified tabs.
  /// - Parameter tabs: The tabs associated with the badge handler.
  public init(tabs: [OGTab]) {
    self.tabs = tabs
  }

  /// Returns the web bridge names associated with the tabs.
  public var webBridgeNames: [String] {
    tabs.map(\.webBridgeName)
  }

  /// Determines if the handler can handle the update action with the given name.
  /// - Parameter name: The name of the update action.
  /// - Returns: `true` if the handler can handle the update action, `false` otherwise.
  public func canHandleUpdateAction(_ name: String) -> Bool {
    webBridgeNames.contains(name)
  }

  /// Handles the update action with the specified name and data.
  /// - Parameters:
  ///   - action: The name of the update action.
  ///   - data: The data associated with the update action.
  public func handleUpdateAction(_ action: String, data: [String: Any]) {
    guard var tab = tabs.first(where: { $0.webBridgeName == action }) else { return }
    let badge = data["body"] as? String ?? "\((data["body"] as? Int) ?? 0)"
    if Int(badge) ?? 0 >= 100 {
      tab.update(badge: "99+")
    } else if badge == "0" {
      tab.update(badge: nil)
    } else {
      tab.update(badge: badge)
    }
    badgePublisher.send(tab)
  }
}
