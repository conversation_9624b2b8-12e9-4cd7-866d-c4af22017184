import Combine
import OGUser<PERSON>ore

// MARK: - LoginWebBridgeActionHandlingMock

public struct LoginWebBridgeActionHandlingMock: LoginWebBridgeActionHandling {
  public var loggedIn: PassthroughSubject<Bool, Never>
  public var webBridgeNames: [String]

  public init(loggedIn: PassthroughSubject<Bool, Never>, webBridgeNames: [String] = []) {
    self.loggedIn = loggedIn
    self.webBridgeNames = webBridgeNames
  }

  public func canHandleUpdateAction(_: String) -> Bool {
    true
  }

  public func handleUpdateAction(_: String, data _: [String: Any]) {}
}
