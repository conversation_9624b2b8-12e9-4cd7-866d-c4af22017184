import Combine
import Foundation
import OGBadge
import OGMacros
import OGMock
import OGN<PERSON>gation<PERSON><PERSON>

@OGMock
public class OGBadgePublisherMock: OGBadgePublishing {
  public var provider: PassthroughSubject<OGNavigationCore.OGTab, Never>

  public init(provider: PassthroughSubject<OGNavigationCore.OGTab, Never>) {
    self.provider = provider
  }

  public func send(_ tab: OGNavigationCore.OGTab) {
    mock.send(tab)
  }
}
