// swift-tools-version: 5.8

import PackageDescription

let package = Package(
  name: "OGBadge",
  platforms: [.iOS(.v15)],
  products: [
    .library(
      name: "OGBadge",
      targets: ["OGBadge"]
    ),
    .library(
      name: "OGBadgeTestsUtils",
      targets: ["OGBadgeTestsUtils"]
    )
  ],
  dependencies: [
    .package(path: "../OGExternalDependencies/OGDIService"),
    .package(path: "../OGWebBridge"),
    .package(path: "../OGNavigation/Packages/OGNavigationCore"),
    .package(path: "../OGUserCore"),
    .package(path: "../OGCore/Packages/OGMock"),
    .package(path: "../OGCore")
  ],
  targets: [
    .target(
      name: "OGBadge",
      dependencies: [
        "OGDIService",
        "OGWebBridge",
        "OGNavigationCore",
        "OGUserCore"
      ]
    ),
    .testTarget(
      name: "OGBadgeTests",
      dependencies: [
        "OGBadge",
        "OGBadgeTestsUtils",
        .product(name: "OGUserCoreTestsUtils", package: "OGUserCore"),
        .product(name: "OGCoreTestsUtils", package: "OGCore")
      ]
    ),
    .target(
      name: "OGBadgeTestsUtils",
      dependencies: [
        "OGBadge",
        "OGNavigationCore",
        "OGMock"
      ],
      path: "TestsUtils"
    )
  ]
)
