import OGNavigationCore
import XCTest

@testable import OGBadge

final class OGBadgeStoreReducerTests: XCTestCase {
  func test_WHEN_updateLoginBadge_THEN_noStateChange() throws {
    var initialState = OGBadgeState.initial
    let expectedState = OGBadgeState.initial

    OGBadgeState.Reducer.reduce(&initialState, with: ._updateLoginBadge)

    XCTAssertEqual(initialState, expectedState)
  }

  func test_WHEN_updateUserIsLoggedIn_equalsTrue_THEN_updateTabWithBadgeContent() throws {
    let initialTab = OGTab(name: "TestTab", rawValue: "TestTab")
    var initialState = OGBadgeState(badgeContent: "Test", tab: initialTab, isLoggedIn: false)
    var expectedTab = OGTab(name: "TestTab", rawValue: "TestTab")
    expectedTab.update(badge: "Test")
    let expectedState = OGBadgeState(badgeContent: "Test", tab: expectedTab, isLoggedIn: true)

    OGBadgeState.Reducer.reduce(&initialState, with: ._updateUserIsLoggedIn(true))

    XCTAssertEqual(initialState, expectedState)
    XCTAssertEqual(initialState.tab.badgeValue, expectedState.tab.badgeValue)
  }

  func test_WHEN_updateUserIsLoggedIn_equalsFalse_THEN_updateTabWithBadgeContentNil() throws {
    var initialTab = OGTab(name: "TestTab", rawValue: "TestTab")
    initialTab.update(badge: "Test")
    var initialState = OGBadgeState(badgeContent: "Test", tab: initialTab, isLoggedIn: false)
    let expectedTab = OGTab(name: "TestTab", rawValue: "TestTab")
    let expectedState = OGBadgeState(badgeContent: "Test", tab: expectedTab, isLoggedIn: false)

    OGBadgeState.Reducer.reduce(&initialState, with: ._updateUserIsLoggedIn(false))

    XCTAssertEqual(initialState.tab.badgeValue, expectedState.tab.badgeValue)
    XCTAssertEqual(initialState, expectedState)
  }

  func test_WHEN_setTab_equalsFalse_THEN_updateState() throws {
    var initialTab = OGTab(name: "TestTab", rawValue: "TestTab")
    initialTab.update(badge: "Test")
    var initialState = OGBadgeState(badgeContent: "Test", tab: initialTab, isLoggedIn: false)
    let expectedTab = OGTab(name: "TestTab", rawValue: "TestTab")
    let expectedState = OGBadgeState(badgeContent: "Test", tab: expectedTab, isLoggedIn: false)

    OGBadgeState.Reducer.reduce(&initialState, with: .setTab(tab: expectedTab, badgeContent: "Test"))

    XCTAssertEqual(initialState.tab.badgeValue, expectedState.tab.badgeValue)
    XCTAssertEqual(initialState, expectedState)
  }

  func test_WHEN_showTabBarBadge_equalsTrue_THEN_updateState() throws {
    var initialTab = OGTab(name: "TestTab", rawValue: "TestTab")
    initialTab.update(badge: "Test")
    var initialState = OGBadgeState(badgeContent: "Test", tab: initialTab, isLoggedIn: false)
    let expectedTab = OGTab(name: "TestTab", rawValue: "TestTab")
    let expectedState = OGBadgeState(badgeContent: "Test", tab: expectedTab, isLoggedIn: false)

    OGBadgeState.Reducer.reduce(&initialState, with: .showTabBarBadge(true))

    XCTAssertEqual(initialState.tab.badgeValue, expectedState.tab.badgeValue)
    XCTAssertEqual(initialState, expectedState)
  }

  func test_WHEN_showTabBarBadge_equalsFalse_THEN_updateState() throws {
    var initialTab = OGTab(name: "TestTab", rawValue: "TestTab")
    initialTab.update(badge: "Test")
    var initialState = OGBadgeState(badgeContent: "Test", tab: initialTab, isLoggedIn: false)
    let expectedTab = OGTab(name: "TestTab", rawValue: "TestTab")
    let expectedState = OGBadgeState(badgeContent: "Test", tab: expectedTab, isLoggedIn: false, isEnabled: false)

    OGBadgeState.Reducer.reduce(&initialState, with: .showTabBarBadge(false))

    XCTAssertEqual(initialState.tab.badgeValue, expectedState.tab.badgeValue)
    XCTAssertEqual(initialState, expectedState)
  }
}
