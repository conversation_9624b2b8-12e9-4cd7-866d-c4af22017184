import Combine
import OGBadgeTestsUtils
import OGNavigationCore
import XCTest

@testable import OGBadge

final class OGBadgeStoreMiddlewareTests: XCTestCase {
  func test_WHEN_updateUserIsLoggedIn_THEN_updateLoginBadge() async throws {
    let provider = PassthroughSubject<OGNavigationCore.OGTab, Never>()
    let sut = OGBadgeState.Middleware(badgePublisher: OGBadgePublisherMock(provider: provider))

    let result = await sut.callAsFunction(action: ._updateUserIsLoggedIn(true), for: .initial)

    let expected = OGBadgeAction._updateLoginBadge
    XCTAssertEqual(result, expected)
  }

  func test_WHEN_updateAccountBadge_THEN_sendTab() async throws {
    let provider = PassthroughSubject<OGNavigationCore.OGTab, Never>()
    let badgePublisherMock = OGBadgePublisherMock(provider: provider)
    let sut = OGBadgeState.Middleware(badgePublisher: badgePublisherMock)
    let expectedTab = OGTab(name: "TestTab", rawValue: "TestTab")

    let state = OGBadgeState(badgeContent: "test", tab: expectedTab, isLoggedIn: false)

    let result = await sut.callAsFunction(action: ._updateLoginBadge, for: state)

    XCTAssertEqual(badgePublisherMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(badgePublisherMock.mock.sendCalls.latestCall, expectedTab)
    XCTAssertNil(result)
  }

  func test_WHEN_setTab_THEN_sendTab() async throws {
    let provider = PassthroughSubject<OGNavigationCore.OGTab, Never>()
    let badgePublisherMock = OGBadgePublisherMock(provider: provider)
    let sut = OGBadgeState.Middleware(badgePublisher: badgePublisherMock)
    let expectedTab = OGTab(name: "TestTab", rawValue: "TestTab")

    let state = OGBadgeState(badgeContent: "test", tab: expectedTab, isLoggedIn: false)

    let result = await sut.callAsFunction(action: .setTab(tab: expectedTab, badgeContent: "test"), for: state)

    XCTAssertEqual(badgePublisherMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(badgePublisherMock.mock.sendCalls.latestCall, expectedTab)
    XCTAssertNil(result)
  }

  func test_WHEN_showTabBarBadge_THEN_sendTab() async throws {
    let provider = PassthroughSubject<OGNavigationCore.OGTab, Never>()
    let badgePublisherMock = OGBadgePublisherMock(provider: provider)
    let sut = OGBadgeState.Middleware(badgePublisher: badgePublisherMock)
    let expectedTab = OGTab(name: "TestTab", rawValue: "TestTab")

    let state = OGBadgeState(badgeContent: "test", tab: expectedTab, isLoggedIn: false)

    let result = await sut.callAsFunction(action: .showTabBarBadge(true), for: state)

    XCTAssertEqual(badgePublisherMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(badgePublisherMock.mock.sendCalls.latestCall, expectedTab)
    XCTAssertNil(result)
  }
}
