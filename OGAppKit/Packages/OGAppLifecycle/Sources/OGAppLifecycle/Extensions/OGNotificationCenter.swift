import Foundation
import NotificationCenter

// MARK: - OGNotificationCenterPublishing

public protocol OGNotificationCenterPublishing {
  func didBecomeActiveNotification() -> NotificationCenter.Publisher
  func didEnterBackgroundNotification() -> NotificationCenter.Publisher
  func didFinishLaunchingPublisher() -> NotificationCenter.Publisher
  func willEnterForegroundPublisher() -> NotificationCenter.Publisher
}

// MARK: - NotificationCenter + OGNotificationCenterPublishing

extension NotificationCenter: OGNotificationCenterPublishing {
  public func didBecomeActiveNotification() -> NotificationCenter.Publisher {
    publisher(for: UIApplication.didBecomeActiveNotification)
  }

  public func didEnterBackgroundNotification() -> NotificationCenter.Publisher {
    publisher(for: UIApplication.didEnterBackgroundNotification)
  }

  public func didFinishLaunchingPublisher() -> NotificationCenter.Publisher {
    publisher(for: UIApplication.didFinishLaunchingNotification)
  }

  public func willEnterForegroundPublisher() -> NotificationCenter.Publisher {
    publisher(for: UIApplication.willEnterForegroundNotification)
  }
}
