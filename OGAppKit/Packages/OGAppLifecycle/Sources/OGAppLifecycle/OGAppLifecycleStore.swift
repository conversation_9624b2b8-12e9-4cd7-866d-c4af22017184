import Combine
import NotificationCenter
import OGDIService
import OGDomainStore
import OGStorage

public typealias OGAppLifecycleStore = OGDomainStore<OGAppLifecycleState, OGAppLifecycleAction>

extension OGDomainStore where State == OGAppLifecycleState, Action == OGAppLifecycleAction {
  public static func make() -> OGAppLifecycleStore {
    OGAppLifecycleStore(
      reducer: OGAppLifecycleState.Reducer.reduce,
      connector: OGAppLifecycleState.Connector()
    )
  }
}

// MARK: - OGAppLifecycleAction

public enum OGAppLifecycleAction: OGDomainAction, Equatable {
  case _didBecomeActive
  case _didEnterBackground
  case _didFinishLaunch
  case _willEnterForeground
}

// MARK: - AppLifeCycleState

public enum AppLifeCycleState {
  case background
  case foreground
  case active
  case started
  case killed
}

// MARK: - OGAppLifecycleState

public struct OGAppLifecycleState: OGDomainState, Equatable {
  public private(set) var isAwaitingUpdate: Bool

  public private(set) var appLifeCycleState: AppLifeCycleState

  public init(
    appLifeCycleState: AppLifeCycleState = .killed,
    isAwaitingUpdate: Bool = false
  ) {
    self.appLifeCycleState = appLifeCycleState
    self.isAwaitingUpdate = isAwaitingUpdate
  }

  public static let initial: Self = .init()
}

// MARK: OGAppLifecycleState.Reducer

extension OGAppLifecycleState {
  public enum Reducer: OGDomainActionReducible {
    public static func reduce(
      _ state: inout OGAppLifecycleState,
      with action: OGAppLifecycleAction
    ) {
      switch action {
      case ._didBecomeActive:
        state.appLifeCycleState = .active

      case ._didEnterBackground:
        state.appLifeCycleState = .background

      case ._didFinishLaunch:
        state.appLifeCycleState = .started

      case ._willEnterForeground:
        state.appLifeCycleState = .foreground
      }
    }
  }
}

// MARK: OGAppLifecycleState.Connector

extension OGAppLifecycleState {
  actor Connector: OGDomainConnector {
    private var cancelationTokens = Set<AnyCancellable>()
    private let notificationCenter: OGNotificationCenterPublishing

    init(notificationCenter: OGNotificationCenterPublishing = NotificationCenter.default) {
      self.notificationCenter = notificationCenter
    }

    func configure(
      dispatch: @escaping (OGAppLifecycleAction) async -> Void
    ) async {
      notificationCenter
        .didBecomeActiveNotification()
        .sink { _ in
          Task {
            await dispatch(._didBecomeActive)
          }
        }
        .store(in: &cancelationTokens)

      notificationCenter
        .didEnterBackgroundNotification()
        .sink { _ in
          Task {
            await dispatch(._didEnterBackground)
          }
        }
        .store(in: &cancelationTokens)

      notificationCenter
        .didFinishLaunchingPublisher()
        .sink { _ in
          Task {
            await dispatch(._didFinishLaunch)
          }
        }
        .store(in: &cancelationTokens)

      notificationCenter
        .willEnterForegroundPublisher()
        .sink { _ in
          Task {
            await dispatch(._willEnterForeground)
          }
        }
        .store(in: &cancelationTokens)
    }
  }
}
