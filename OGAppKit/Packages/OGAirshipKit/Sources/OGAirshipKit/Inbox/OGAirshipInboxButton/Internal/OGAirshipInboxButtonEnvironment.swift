// swiftlint:disable type_name

import SwiftUI

// MARK: - OGAirshipInboxButtonStyleKey

struct OGAirshipInboxButtonStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGAirshipInboxButtonStyle(style: DefaultOGAirshipInboxButtonStyle())
}

extension EnvironmentValues {
  var styleOGAirshipInboxButton: AnyOGAirshipInboxButtonStyle {
    get { self[OGAirshipInboxButtonStyleKey.self] }
    set { self[OGAirshipInboxButtonStyleKey.self] = newValue }
  }
}
