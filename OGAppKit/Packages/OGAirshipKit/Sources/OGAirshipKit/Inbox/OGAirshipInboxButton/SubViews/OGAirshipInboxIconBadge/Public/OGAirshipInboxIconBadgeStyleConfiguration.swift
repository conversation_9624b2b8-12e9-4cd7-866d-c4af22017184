// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGAirshipInboxIconBadge.
public struct OGAirshipInboxIconBadgeStyleConfiguration {
  /// A view that describes the content of a the OGAirshipInboxIconBadge.
  public let content: OGAirshipInboxIconBadgeStyleConfiguration.Content

  /// The type-erased content of a OGAirshipInboxIconBadge.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public private(set) var body: AnyView
  }
}
