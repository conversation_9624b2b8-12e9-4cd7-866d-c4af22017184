import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGAirshipInboxIconBadgeStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGAirshipInboxIconBadge instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGAirshipInboxIconBadgeStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGAirshipInboxIconBadgeStyle``.
  /// - Returns: A view that represents a OGAirshipInboxIconBadge.
  @ViewBuilder
  public func register(_ style: some OGAirshipInboxIconBadgeStyle) -> some View {
    environment(\.styleOGAirshipInboxIconBadge, AnyOGAirshipInboxIconBadgeStyle(style: style))
  }
}
