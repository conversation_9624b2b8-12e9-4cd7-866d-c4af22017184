import SwiftUI

/// A type that applies custom appearance to
/// all OGAirshipInboxIconBadges within a view hierarchy.
public protocol OGAirshipInboxIconBadgeStyle {
  /// A view that represents the body of a OGAirshipInboxIconBadge.
  associatedtype Body: View

  /// The properties of a OGAirshipInboxIconBadge.
  typealias Configuration = OGAirshipInboxIconBadgeStyleConfiguration

  /// Creates a view that represents the body of a OGAirshipInboxIconBadge.
  ///
  /// The system calls this method for each OGAirshipInboxIconBadge instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGAirshipInboxIconBadgeStyle``.
  /// ```swift
  /// struct MyCustomOGAirshipInboxIconBadgeStyle: OGAirshipInboxIconBadgeStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGAirshipInboxIconBadgeConfiguration`` of the  OGAirshipInboxIconBadge.
  /// - Returns: A view that represents a OGAirshipInboxIconBadge.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
