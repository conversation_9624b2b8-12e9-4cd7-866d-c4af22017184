import SwiftUI

/// A type that applies custom appearance to
/// all OGAirshipInboxIcons within a view hierarchy.
public protocol OGAirshipInboxIconStyle {
  /// A view that represents the body of a OGAirshipInboxIcon.
  associatedtype Body: View

  /// The properties of a OGAirshipInboxIcon.
  typealias Configuration = OGAirshipInboxIconStyleConfiguration

  /// Creates a view that represents the body of a OGAirshipInboxIcon.
  ///
  /// The system calls this method for each OGAirshipInboxIcon instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGAirshipInboxIconStyle``.
  /// ```swift
  /// struct MyCustomOGAirshipInboxIconStyle: OGAirshipInboxIconStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGAirshipInboxIconConfiguration`` of the  OGAirshipInboxIcon.
  /// - Returns: A view that represents a OGAirshipInboxIcon.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
