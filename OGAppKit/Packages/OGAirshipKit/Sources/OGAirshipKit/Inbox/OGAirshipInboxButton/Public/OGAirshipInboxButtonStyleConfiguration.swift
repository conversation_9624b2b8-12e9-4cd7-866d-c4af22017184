// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGAirshipInboxButton.
public struct OGAirshipInboxButtonStyleConfiguration {
  /// A view that describes the content of the OGAirshipInboxButton.
  public let content: Content
  /// An integer that describes the number of notifications of the OGAirshipInboxButton.
  public let unreadCount: Int

  public init(content: Content, unreadCount: Int) {
    self.content = content
    self.unreadCount = unreadCount
  }

  /// The type-erased content of a OGAirshipInboxButton.
  public struct Content: View {
    private let storage: AnyView

    public init(content: some View, unreadCount _: Int) {
      self.storage = AnyView(content)
    }

    public var body: some View {
      storage
    }
  }
}
