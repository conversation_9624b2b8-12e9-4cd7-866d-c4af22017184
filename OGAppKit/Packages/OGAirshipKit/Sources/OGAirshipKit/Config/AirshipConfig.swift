import AirshipCore
import Foundation

// MARK: - OGAirshipConfiguring

public protocol OGAirshipConfiguring {
  var productionAppKey: String? { get set }
  var productionAppSecret: String? { get set }
  var cloudSite: String { get set }
}

// MARK: - AirshipConfig + OGAirshipConfiguring

extension AirshipConfig: OGAirshipConfiguring {
  public var cloudSite: String {
    get {
      switch site {
      case .us:
        "us"
      case .eu:
        "eu"
      }
    }
    set { site = newValue.lowercased() == "us" ? .us : .eu }
  }
}
