import OGCore
import OGDIService
import OGSecret

// MARK: - OGAirshipSecretProduction

public final class OGAirshipSecretProduction: OGSecretAdapter {
  override public class var secretName: OGSecret.Name { "pushSecret" } // pushPrivateKey
}

// MARK: - OGAirshipSecretBeta

public final class OGAirshipSecretBeta: OGSecretAdapter {
  override public class var secretName: OGSecret.Name { "pushBetaSecret" } // sandbox
}

// MARK: - OGAirshipAppKeyProduction

public final class OGAirshipAppKeyProduction: OGSecretAdapter {
  override public class var secretName: OGSecret.Name { "pushAppKey" } // pushProductionAppId
}

// MARK: - OGAirshipAppKeyBeta

public final class OGAirshipAppKeyBeta: OGSecretAdapter {
  override public class var secretName: OGSecret.Name { "pushBetaAppKey" } // pushSandboxAppId
}

// MARK: - OGAirshipSecretProviding

public protocol OGAirshipSecretProviding {
  var secret: String? { get }
  var key: String? { get }
}

// MARK: - OGAirshipSecret

public struct OGAirshipSecret: OGAirshipSecretProviding {
  private let appEnvironment: OGAppEnvironmental
  private let productionSecret: OGSecretAdapter
  private let betaSecret: OGSecretAdapter
  private let productionKey: OGSecretAdapter
  private let betaKey: OGSecretAdapter
  public init(
    appEnvironment: OGAppEnvironmental = OGCoreContainer.shared.appEnvironment(),
    productionSecret: OGSecretAdapter = OGAirshipSecretProduction(),
    betaSecret: OGSecretAdapter = OGAirshipSecretBeta(),
    productionKey: OGSecretAdapter = OGAirshipAppKeyProduction(),
    betaKey: OGSecretAdapter = OGAirshipAppKeyBeta()
  ) {
    self.appEnvironment = appEnvironment
    self.productionSecret = productionSecret
    self.betaSecret = betaSecret
    self.productionKey = productionKey
    self.betaKey = betaKey
  }

  public var secret: String? {
    appEnvironment.isDebugBuild ? betaSecret.secret : productionSecret.secret
  }

  public var key: String? {
    appEnvironment.isDebugBuild ? betaKey.secret : productionKey.secret
  }
}
