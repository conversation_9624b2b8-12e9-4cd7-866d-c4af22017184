import AirshipCore
import Foundation
import OGDIService

// MARK: - OGAirshipAttribute

public struct OGAirshipAttribute {
  public let name: String
  public let value: Any

  public init(name: String, value: Any) {
    self.name = name
    self.value = value
  }

  public func toDictionary() -> [String: Any] {
    [name: value]
  }
}

// MARK: - AttributesEditing

public protocol AttributesEditing {
  func remove(_ attribute: String)
  func set(date: Date, attribute: String)
  func set(double: Double, attribute: String)
  func set(int: Int, attribute: String)
  func set(string: String, attribute: String)
  func set(float: Float, attribute: String)
  func set(uint: UInt, attribute: String)
  func apply()
}

// MARK: - ChannelEditing

public protocol ChannelEditing {
  func editAttributes(editorBlock: (AttributesEditing) -> Void)
  func editTags(_ editorBlock: (TagEditor) -> Void)
}

// MARK: - AttributesEditor + AttributesEditing

extension AttributesEditor: AttributesEditing {}

// MARK: - AirshipChannel + ChannelEditing

extension AirshipChannel: ChannelEditing {
  public func editAttributes(editorBlock: (AttributesEditing) -> Void) {
    editAttributes(editorBlock)
  }
}

// MARK: - OGAirshipChannelServing

public protocol OGAirshipChannelServing {
  func setAttribute(_ attribute: OGAirshipAttribute)
  func set(attributes: [String: Any])
  func remove(keys: [String])
  func setLoggedInTag(_ value: Bool)
}

// MARK: - OGAirshipChannelService

public struct OGAirshipChannelService: OGAirshipChannelServing {
  private let channelEditor: ChannelEditing
  private let airshipTrackingFeature: AirshipTrackingFeatureAdaptable
  public init(
    channelEditor: ChannelEditing = Airship.channel,
    airshipTrackingFeature: AirshipTrackingFeatureAdaptable = OGAirshipTrackingFeatureAdapterContainer.shared.airshipTracking()
  ) {
    self.channelEditor = channelEditor
    self.airshipTrackingFeature = airshipTrackingFeature
  }

  public func setLoggedInTag(_ value: Bool) {
    channelEditor.editTags { editor in
      value ? editor.add("loggedIn") : editor.remove("loggedIn")
    }
  }

  public func setAttribute(_ attribute: OGAirshipAttribute) {
    set(attributes: attribute.toDictionary())
  }

  public func set(attributes: [String: Any]) {
    attributes.forEach {
      let value = $0.value
      let key = mapAttributeKey(key: $0.key)
      channelEditor.editAttributes { editor in
        if let string = value as? String {
          editor.set(string: string, attribute: key)
        } else if let date = value as? Date {
          editor.set(date: date, attribute: key)
        } else if let int = value as? Int {
          editor.set(int: int, attribute: key)
        } else if let double = value as? Double {
          editor.set(double: double, attribute: key)
        }
      }
    }
  }

  public func remove(keys: [String]) {
    keys.forEach {
      let key = mapAttributeKey(key: $0)
      channelEditor.editAttributes { editor in
        editor.remove(key)
      }
    }
  }

  private func mapAttributeKey(key: String) -> String {
    airshipTrackingFeature.configuration.value.attributeIdMapping[key] ?? key
  }
}
