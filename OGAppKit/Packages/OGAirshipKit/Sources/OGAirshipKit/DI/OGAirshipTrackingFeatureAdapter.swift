// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import AirshipCore
import Combine
import Foundation
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros

// MARK: - AirshipTrackingFeatureConfigurable

public protocol AirshipTrackingFeatureConfigurable {
  var isEnabled: Bool { get set }
  var attributeIdMapping: [String: String] { get set }
  var eventIdMapping: [String: Bool] { get set }
  var keepContactAssociation: Bool { get set }
}

// MARK: - OGAirshipTrackingFeatureAdapter

public final class OGAirshipTrackingFeatureAdapter: OGFeatureAdapter, AirshipTrackingFeatureAdaptable {
  override public class var featureName: OGFeature.Name { OGIdentifier.airshipTracking.value }

  public let configuration: CurrentValueSubject<AirshipTrackingFeatureConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()

  public init(configuration: AirshipTrackingFeatureConfigurable?) {
    guard let configuration else {
      fatalError("The AirshipFeatureConfiguration has not been registered")
    }
    self.configuration = CurrentValueSubject(configuration)
    super.init()

    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature
      .sink { [weak self] feature in
        guard let self else { return }
        var updatedConfiguration = self.configuration.value
        guard let feature else {
          updatedConfiguration.isEnabled = false
          self.configuration.send(updatedConfiguration)
          return
        }
        updatedConfiguration.isEnabled = feature.isEnabled

        let attributeIdMapping: [String: String] = feature.customValues(for: OGFeatureKey.CustomValues.AirshipTracking.attributeIdMapping)
        updatedConfiguration.attributeIdMapping = attributeIdMapping
        let eventIdMapping: [String: Bool] = feature.customValues(for: OGFeatureKey.CustomValues.AirshipTracking.eventIdMapping)
        updatedConfiguration.eventIdMapping = eventIdMapping
        let keepContactAssociation: Bool = (feature.customValue(for: OGFeatureKey.CustomValues.AirshipTracking.keepContactAssociation)) ?? self.configuration.value.keepContactAssociation
        updatedConfiguration.keepContactAssociation = keepContactAssociation
        self.configuration.send(updatedConfiguration)
      }
      .store(in: &subscriptions)
  }
}

// MARK: - AirshipTrackingFeatureAdaptable

public protocol AirshipTrackingFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<AirshipTrackingFeatureConfigurable, Never> { get }
}

// MARK: - OGFeatureKey.CustomValues.AirshipTracking

extension OGFeatureKey.CustomValues {
  public enum AirshipTracking: String, OGKeyReceivable {
    public var value: String {
      rawValue
    }

    case attributeIdMapping
    case eventIdMapping
    case keepContactAssociation
  }
}

extension OGIdentifier {
  public static let airshipTracking = #identifier("airshipTracking")
}
