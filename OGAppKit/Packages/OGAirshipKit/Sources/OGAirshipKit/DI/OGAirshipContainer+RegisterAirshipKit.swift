import AirshipCore
import OGCore
import UIKit

extension OGAirshipContainer {
  @MainActor
  public static func registerAirshipKit(
    launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) {
    let airshipKit = OGAirshipKit(
      launchOptions: launchOptions,
      takeOff: Airship.takeOff(_:launchOptions:)
    )
    OGAirshipContainer.shared.airshipKit.register {
      airshipKit
    }

    let logger = OGCoreContainer.shared.logger()
    logger.log(
      .debug,
      domain: .service,
      message: "Airship push token: \(Airship.push.deviceToken ?? "NAN")"
    )
    logger.log(
      .debug,
      domain: .tracking,
      message: "Airship channel identifier: \(Airship.channel.identifier ?? "NAN")"
    )
  }
}
