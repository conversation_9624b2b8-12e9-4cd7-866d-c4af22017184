// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import AirshipCore
import Combine
import Foundation
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros

// MARK: - AirshipFeatureConfigurable

public protocol AirshipFeatureConfigurable {
  var isEnabled: Bool { get set }
  var cloudSite: String { get set }
}

// MARK: - OGAirshipFeatureAdapter

public final class OGAirshipFeatureAdapter: OGFeatureAdapter, AirshipFeatureAdaptable {
  override public class var featureName: OGFeature.Name { OGIdentifier.airship.value }

  public let configuration: CurrentValueSubject<AirshipFeatureConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()

  public init(configuration: AirshipFeatureConfigurable?) {
    guard let configuration else {
      fatalError("The AirshipFeatureConfiguration has not been registered")
    }
    self.configuration = CurrentValueSubject(configuration)
    super.init()

    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature
      .sink { [weak self] feature in
        guard let self else { return }
        var updatedConfiguration = self.configuration.value
        guard let feature else {
          updatedConfiguration.isEnabled = false
          self.configuration.send(updatedConfiguration)
          return
        }
        updatedConfiguration.isEnabled = feature.isEnabled
        let cloudSite: String = (feature.customValue(for: OGFeatureKey.CustomValues.Airship.cloudSite)) ?? self.configuration.value.cloudSite
        updatedConfiguration.cloudSite = cloudSite
        self.configuration.send(updatedConfiguration)
      }
      .store(in: &subscriptions)
  }
}

// MARK: - AirshipFeatureAdaptable

public protocol AirshipFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<AirshipFeatureConfigurable, Never> { get }
}

// MARK: - OGFeatureKey.CustomValues.Airship

extension OGFeatureKey.CustomValues {
  public enum Airship: String, OGKeyReceivable {
    public var value: String {
      rawValue
    }

    case cloudSite
  }
}

extension OGIdentifier {
  public static let airship = #identifier("airship")
}
