// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import AirshipCore
import Combine
import Foundation
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros

// MARK: - AirshipPushFeatureConfigurable

public protocol AirshipPushFeatureConfigurable {
  var isEnabled: Bool { get set }
}

// MARK: - AirshipPushFeatureConfig

public struct AirshipPushFeatureConfig: AirshipPushFeatureConfigurable {
  public var isEnabled: Bool = false
  public init(isEnabled: Bool = false) {
    self.isEnabled = isEnabled
  }
}

// MARK: - OGAirshipPushFeatureAdapter

public final class OGAirshipPushFeatureAdapter: OGFeatureAdapter, AirshipPushFeatureAdaptable {
  override public class var featureName: OGFeature.Name { OGIdentifier.airshipPush.value }

  public let configuration: CurrentValueSubject<AirshipPushFeatureConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()

  public init(configuration: AirshipPushFeatureConfigurable = AirshipPushFeatureConfig()) {
    self.configuration = CurrentValueSubject(configuration)
    super.init()

    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature
      .sink { [weak self] feature in
        guard let self else { return }
        var updatedConfiguration = self.configuration.value
        guard let feature else {
          updatedConfiguration.isEnabled = false
          self.configuration.send(updatedConfiguration)
          return
        }
        updatedConfiguration.isEnabled = feature.isEnabled
        self.configuration.send(updatedConfiguration)
      }
      .store(in: &subscriptions)
  }
}

// MARK: - AirshipPushFeatureAdaptable

public protocol AirshipPushFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<AirshipPushFeatureConfigurable, Never> { get }
}

extension OGIdentifier {
  public static let airshipPush = #identifier("push")
}
