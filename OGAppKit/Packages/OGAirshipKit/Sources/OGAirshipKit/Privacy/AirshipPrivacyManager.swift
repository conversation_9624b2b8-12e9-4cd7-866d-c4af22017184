import AirshipCore
import AirshipMessageCenter

// MARK: - AirshipPrivacyManaging

public protocol AirshipPrivacyManaging {
  var enabledFeatures: AirshipFeature { get }
  func enableFeatures(_ features: AirshipFeature)
  func disableFeatures(_ features: AirshipFeature)
}

// MARK: - AirshipPrivacyManager + AirshipPrivacyManaging

extension AirshipPrivacyManager: AirshipPrivacyManaging {}

// MARK: - OGAirshipPrivacyManager

public struct OGAirshipPrivacyManager: AirshipPrivacyManaging {
  private let privacyManager: AirshipPrivacyManaging

  public var enabledFeatures: AirshipFeature {
    privacyManager.enabledFeatures
  }

  init(
    privacyManager: AirshipPrivacyManaging = Airship.shared.privacyManager
  ) {
    self.privacyManager = privacyManager
  }

  public func enableFeatures(_ features: AirshipFeature) {
    privacyManager.enableFeatures(features)
  }

  public func disableFeatures(_ features: AirshipFeature) {
    privacyManager.disableFeatures(features)
  }
}

extension AirshipFeature {
  public static let ogkit: AirshipFeature = [.analytics, .tagsAndAttributes]
}
