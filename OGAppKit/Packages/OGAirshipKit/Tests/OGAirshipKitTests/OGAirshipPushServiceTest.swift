import Combine
@testable import OGAirshipKit
import OGAirshipKitTestsUtils
import OGTenantCore
import OGTenantCoreTestsUtils
import OGTenantKit
import OGTenantKitTestsUtils
import XCTest

final class OGAirshipPushServiceTest: XCTestCase {
  var airshipChannelMock: OGAirshipChannelServiceMock!
  var airshipPushSetupMock: OGAirshipPushSetupMock!
  var observerServiceMock: OGTenantObserverMock!

  override func setUp() async throws {
    airshipChannelMock = OGAirshipChannelServiceMock()
    airshipPushSetupMock = OGAirshipPushSetupMock()
    observerServiceMock = OGTenantContainer.shared.observerService() as? OGTenantObserverMock
    let stub: OGTenant? = OGTenant.stub
    observerServiceMock?.mock.selectedTenant.getter.mockCall {
      var published = Published(initialValue: stub)
      return published.projectedValue
    }

    OGAirshipFeatureAdapterContainer.shared.airshipPush.register {
      OGAirshipPushFeatureAdapterMock(isEnabled: true)
    }
  }

  override func tearDown() async throws {
    airshipChannelMock = nil
    airshipPushSetupMock = nil
    observerServiceMock = nil
    OGAirshipFeatureAdapterContainer.shared.airshipPush.reset()
  }

  func test_GIVEN_setup_THEN_push_setup_called() async throws {
    let _ = OGAirshipPushService(
      airshipChannel: airshipChannelMock,

      airshipPushSetup: airshipPushSetupMock,
      authorizationStatus: { .authorized },
      userPushNotificationsEnabled: { _ in }
    )

    XCTAssertEqual(airshipPushSetupMock.mock.setupPushCalls.callsCount, 1)
    XCTAssertNotNil(airshipPushSetupMock.mock.setupPushCalls.latestCall)
  }

  func test_GIVEN_pushFeature_THEN_airship_isEnabled() async throws {
    var result = false
    let userPushNotificationsEnabled = expectation(description: "userPushNotificationsEnabled")
    let _ = OGAirshipPushService(
      airshipChannel: airshipChannelMock,
      airshipPushSetup: airshipPushSetupMock,
      authorizationStatus: { .authorized },
      userPushNotificationsEnabled: { isEnabled in
        result = isEnabled
        userPushNotificationsEnabled.fulfill()
      }
    )
    await fulfillment(of: [userPushNotificationsEnabled], timeout: 0.5)
    XCTAssertTrue(result)
  }

  func test_GIVEN_setup_THEN_airship_channel_called() async throws {
    let stub: OGTenant? = OGTenant.austria
    observerServiceMock?.mock.selectedTenant.getter.mockCall {
      var published = Published(initialValue: stub)
      return published.projectedValue
    }

    let _ = OGAirshipPushService(
      airshipChannel: airshipChannelMock,
      airshipPushSetup: airshipPushSetupMock,
      authorizationStatus: { .authorized },
      userPushNotificationsEnabled: { _ in }
    )
    XCTAssertEqual(airshipChannelMock.mock.setCalls.callsCount, 1)
    XCTAssertEqual(airshipChannelMock.mock.setCalls.latestCall as? [String: String?], ["shopCountryCode": OGTenant.austria.locale.regionCode?.lowercased()])
  }

  func test_GIVEN_setup_With_DECH_Tenant_THEN_airship_channel_called() async throws {
    let stub: OGTenant? = OGTenant.switzerlandDE
    observerServiceMock?.mock.selectedTenant.getter.mockCall {
      var published = Published(initialValue: stub)
      return published.projectedValue
    }

    let _ = OGAirshipPushService(
      airshipChannel: airshipChannelMock,
      airshipPushSetup: airshipPushSetupMock,
      authorizationStatus: { .authorized },
      userPushNotificationsEnabled: { _ in }
    )
    XCTAssertEqual(airshipChannelMock.mock.setCalls.callsCount, 1)
    XCTAssertEqual(airshipChannelMock.mock.setCalls.latestCall as? [String: String?], ["shopCountryCode": "dech"])
  }

  func test_GIVEN_setup_With_FRCH_Tenant_THEN_airship_channel_called() async throws {
    let stub: OGTenant? = OGTenant.switzerlandFR
    observerServiceMock?.mock.selectedTenant.getter.mockCall {
      var published = Published(initialValue: stub)
      return published.projectedValue
    }

    let _ = OGAirshipPushService(
      airshipChannel: airshipChannelMock,
      airshipPushSetup: airshipPushSetupMock,
      authorizationStatus: { .authorized },
      userPushNotificationsEnabled: { _ in }
    )
    XCTAssertEqual(airshipChannelMock.mock.setCalls.callsCount, 1)
    XCTAssertEqual(airshipChannelMock.mock.setCalls.latestCall as? [String: String?], ["shopCountryCode": "frch"])
  }

  func test_GIVEN_notificationRegistrationFinished_WITH_provisional_THEN_airship_channel_set() async throws {
    let sut = OGAirshipPushService(
      airshipChannel: airshipChannelMock,

      airshipPushSetup: airshipPushSetupMock,
      authorizationStatus: { .authorized },
      userPushNotificationsEnabled: { _ in }
    )
    sut.notificationRegistrationFinished(withAuthorizedSettings: .alert, categories: Set([]), status: .provisional)

    XCTAssertEqual(airshipChannelMock.mock.setCalls.callsCount, 2)
    XCTAssertEqual(Set((airshipChannelMock.mock.setCalls.latestCall?.keys.map { $0 as String }) ?? []), Set(["provisional_authorization_ts", "provisional_authorization"]))
    XCTAssertNotNil(airshipChannelMock.mock.setCalls.latestCall?.values.first(where: { $0 is Date }))
    XCTAssertEqual(airshipChannelMock.mock.setCalls.latestCall?.values.first(where: { $0 is String }) as? String, "true")
  }

  func test_GIVEN_notificationRegistrationFinished_WITH_nonProvisional_THEN_airship_channel_removed() throws {
    let sut = OGAirshipPushService(
      airshipChannel: airshipChannelMock,

      airshipPushSetup: airshipPushSetupMock,
      authorizationStatus: { .authorized },
      userPushNotificationsEnabled: { _ in }
    )
    sut.notificationRegistrationFinished(withAuthorizedSettings: .alert, categories: Set([]), status: .authorized)

    XCTAssertEqual(airshipChannelMock.mock.removeCalls.callsCount, 1)
    XCTAssertEqual(Set(airshipChannelMock.mock.removeCalls.latestCall!), Set(["provisional_authorization_ts", "provisional_authorization"]))
  }

  func test_GIVEN_notificationAuthorizedSettingsDidChange_WITH_provisional_THEN_airship_channel_set() async throws {
    let sut = OGAirshipPushService(
      airshipChannel: airshipChannelMock,

      airshipPushSetup: airshipPushSetupMock,
      authorizationStatus: { .provisional },
      userPushNotificationsEnabled: { _ in }
    )
    sut.notificationAuthorizedSettingsDidChange(.alert)

    XCTAssertEqual(airshipChannelMock.mock.setCalls.callsCount, 2)
    XCTAssertEqual(Set((airshipChannelMock.mock.setCalls.latestCall?.keys.map { $0 as String }) ?? []), Set(["provisional_authorization_ts", "provisional_authorization"]))
    XCTAssertNotNil(airshipChannelMock.mock.setCalls.latestCall?.values.first(where: { $0 is Date }))
    XCTAssertEqual(airshipChannelMock.mock.setCalls.latestCall?.values.first(where: { $0 is String }) as? String, "true")
  }

  func test_GIVEN_notificationAuthorizedSettingsDidChange_WITH_nonProvisional_THEN_airship_channel_removed() async throws {
    let sut = OGAirshipPushService(
      airshipChannel: airshipChannelMock,

      airshipPushSetup: airshipPushSetupMock,
      authorizationStatus: { .authorized },
      userPushNotificationsEnabled: { _ in }
    )
    sut.notificationAuthorizedSettingsDidChange(.alert)

    XCTAssertEqual(airshipChannelMock.mock.removeCalls.callsCount, 1)
    XCTAssertEqual(Set(airshipChannelMock.mock.removeCalls.latestCall!), Set(["provisional_authorization_ts", "provisional_authorization"]))
  }
}
