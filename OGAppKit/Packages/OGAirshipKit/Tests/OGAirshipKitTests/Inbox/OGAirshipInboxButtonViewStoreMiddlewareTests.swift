import OGIdentifier
import <PERSON><PERSON>out<PERSON>
import OGRouterTestsUtils
import XCTest

@testable import OGAirshipKit

final class OGAirshipInboxButtonViewStoreMiddlewareTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_updated_THEN_noNextEvent() async {
    let routerMock = OGRoutePublisherMock()

    let sut = OGAirshipInboxButton.Middleware(router: routerMock)
    let nextEvent = await sut.callAsFunction(
      event: ._updated(unreadCount: 0),
      for: .initial
    )
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_enabled_THEN_noNextEvent() async {
    let routerMock = OGRoutePublisherMock()

    let sut = OGAirshipInboxButton.Middleware(router: routerMock)
    let nextEvent = await sut.callAsFunction(
      event: ._enabled(true),
      for: .initial
    )
    XCTAssertNil(nextEvent)
  }

  func test_WHEN_didTap_THEN_sendRoute_AND_noNextEvent() async {
    let routerMock = OGRoutePublisherMock()
    let sut = OGAirshipInboxButton.Middleware(router: routerMock)
    let state = OGAirshipInboxButton.ViewState(unreadCount: 1, isEnabled: true)
    let nextEvent = await sut.callAsFunction(
      event: .didTap,
      for: state
    )
    let route = OGRoute(OGIdentifier.inbox.value)

    XCTAssertNil(nextEvent)
    XCTAssertEqual(1, routerMock.mock.sendCalls.callsCount)
    XCTAssertEqual(route, routerMock.mock.sendCalls.latestCall)
  }
}
