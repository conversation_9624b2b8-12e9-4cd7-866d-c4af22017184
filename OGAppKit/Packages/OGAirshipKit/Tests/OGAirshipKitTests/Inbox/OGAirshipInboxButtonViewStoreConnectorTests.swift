import OGAirshipKitTestsUtils
import OGCoreTestsUtils
import XCTest

@testable import OGAirshipKit

// MARK: - OGAirshipInboxButtonViewStoreConnectorTests

final class OGAirshipInboxButtonViewStoreConnectorTests: XCTestCase {
  func test_WHEN_received_THEN_dispatched() async {
    await withMainSerialExecutor {
      let exp = expectation(description: "Updated messages event dispatched")
      let sut = OGAirshipInboxButton.Connector(
        inboxStore: OGAirshipInboxStore.makeMock(),
        inboxFeature: InboxFeatureAdapter(
          configuration: InboxFeatureConfig.stub
        )
      )
      // await inboxStoreMock.dispatch(._set(messages: .stub, unreadCount: 0))
      var actualEvents: [OGAirshipInboxButton.Event] = []
      await sut.configure { event in
        actualEvents.append(event)
        if actualEvents.count == 2 {
          exp.fulfill()
        }
      }

      await fulfillment(of: [exp])

      XCTAssertEqual(
        Set([
          ._enabled(false),
          ._updated(unreadCount: 0)
        ]),
        Set(actualEvents)
      )
    }
  }
}

// MARK: - OGAirshipInboxButton.Event + Hashable

extension OGAirshipInboxButton.Event: Hashable {
  public func hash(into hasher: inout Hasher) {
    switch self {
    case let ._enabled(value):
      hasher.combine(value)
    case let ._updated(unreadCount: value):
      hasher.combine(value)
    case .didTap:
      break
    }
  }
}
