import OGAirshipKit

extension AirshipFeatureConfigurable {
  public static var stub: any AirshipFeatureConfigurable {
    AirshipFeatureConfig.stub
  }
}

extension AirshipFeatureConfig {
  public static var stub: AirshipFeatureConfig {
    AirshipFeatureConfig()
  }
}

// MARK: - AirshipFeatureConfig

public struct AirshipFeatureConfig: AirshipFeatureConfigurable {
  public var attributeIdMapping: [String: String]
  public var cloudSite: String
  public var eventIdMapping: [String: Bool]
  public var isEnabled: Bool
  public var keepContactAssociation: Bool

  public init(
    attributeIdMapping: [String: String] = [:],
    cloudSite: String = "eu",
    eventIdMapping: [String: Bool] = [:],
    isEnabled: Bool = true,
    keepContactAssociation: Bool = true
  ) {
    self.attributeIdMapping = attributeIdMapping
    self.cloudSite = cloudSite
    self.eventIdMapping = eventIdMapping
    self.isEnabled = isEnabled
    self.keepContactAssociation = keepContactAssociation
  }
}
