import OGAirshipKit
import OGDomainStore
import OGDomainStoreTestsUtils

extension OGAirshipInboxStore {
  public static func makeMock(
    initialState: OGAirshipInboxState = .initial,
    reducerMock: OGDomainReducerMock<State, Action> = OGDomainReducerMock(),
    middlewareMock: OGDomainMiddlewareMock<State, Action> = OGDomainMiddlewareMock()
  )
    -> OGAirshipInboxStore {
    OGAirshipInboxStore(
      initialState: initialState,
      reducer: reducerMock.reduce,
      middlewares: middlewareMock
    )
  }

  public static var mock: OGDomainStoreMock<OGAirshipInboxState, OGAirshipInboxAction> {
    OGDomainStoreMock<OGAirshipInboxState, OGAirshipInboxAction>()
  }
}
