import Combine
import Foundation
import OGAirshipKit
import OGCore
import OGDIService
import OGMacros
import OGMock

// MARK: - OGAirshipFeatureAdapterMock

@OGMock
public final class OGAirshipPushFeatureAdapterMock: AirshipPushFeatureAdaptable {
  public init() {}
  public var configuration: CurrentValueSubject<any AirshipPushFeatureConfigurable, Never> {
    get {
      mock.configuration.getter.record()
    }
    set {
      mock.configuration.setter.record(newValue)
    }
  }

  public var isAdaptedFeatureEnabled: Bool {
    get {
      mock.isAdaptedFeatureEnabled.getter.record()
    }
    set {
      mock.isAdaptedFeatureEnabled.setter.record(newValue)
    }
  }

  public init(isEnabled: Bool = true) {
    mock.configuration.getter.mockCall { _ in
      CurrentValueSubject(AirshipPushFeatureConfig(isEnabled: isEnabled))
    }
  }
}
