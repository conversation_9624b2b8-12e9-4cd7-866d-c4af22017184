import Combine
import Foundation
import OGAirshipKit
import OGCore
import OGDIService
import OGMacros
import OGMock

// MARK: - OGAirshipFeatureAdapterMock

@OGMock
public final class OGAirshipFeatureAdapterMock: AirshipFeatureAdaptable {
  public init() {}
  public var configuration: CurrentValueSubject<any AirshipFeatureConfigurable, Never> {
    get {
      mock.configuration.getter.record()
    }
    set {
      mock.configuration.setter.record(newValue)
    }
  }

  public var isAdaptedFeatureEnabled: Bool {
    get {
      mock.isAdaptedFeatureEnabled.getter.record()
    }
    set {
      mock.isAdaptedFeatureEnabled.setter.record(newValue)
    }
  }

  public init(isEnabled: Bool = true) {
    mock.configuration.getter.mockCall { _ in
      CurrentValueSubject(AirshipFeatureConfig(isEnabled: isEnabled))
    }
  }
}

// MARK: - OGAirshipFeatureAdapterContainer + AutoRegistering

extension OGAirshipFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    guard OGCoreContainer.shared.appEnvironment().isTestsBuild else { return }
    airship.register {
      OGAirshipFeatureAdapterMock()
    }
  }
}
