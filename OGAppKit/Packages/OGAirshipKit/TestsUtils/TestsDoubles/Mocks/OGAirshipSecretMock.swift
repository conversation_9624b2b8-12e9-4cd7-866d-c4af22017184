import Combine
import Foundation
import OGAirshipKit
import OGMacros
import OGMock

@OGMock
public class OGAirshipSecretMock: OGAirshipSecretProviding {
  public init() {}
  public var secret: String? {
    get {
      mock.secret.getter.record()
    }
    set {
      mock.secret.setter.record(newValue)
    }
  }

  public var key: String? {
    get {
      mock.key.getter.record()
    }
    set {
      mock.key.setter.record(newValue)
    }
  }
}
