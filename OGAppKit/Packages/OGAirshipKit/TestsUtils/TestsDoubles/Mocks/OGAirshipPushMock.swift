import AirshipCore
import Foundation
import OGAirshipKit
import OGMacros
import OGMock

@OGMock
public final class OGAirshipPushMock: OGAirshipPushServicing {
  public init() {}
  public var defaultPresentationOptions: UNNotificationPresentationOptions {
    get {
      mock.defaultPresentationOptions.getter.record()
    }
    set {
      mock.defaultPresentationOptions.setter.record(newValue)
    }
  }

  public var notificationOptions: UANotificationOptions {
    get {
      mock.notificationOptions.getter.record()
    }
    set {
      mock.notificationOptions.setter.record(newValue)
    }
  }

  public var userPushNotificationsEnabled: Bool {
    get {
      mock.userPushNotificationsEnabled.getter.record()
    }
    set {
      mock.userPushNotificationsEnabled.setter.record(newValue)
    }
  }

  public var autobadgeEnabled: Bool {
    get {
      mock.autobadgeEnabled.getter.record()
    }
    set {
      mock.autobadgeEnabled.setter.record(newValue)
    }
  }

  public var pushNotificationDelegate: AirshipCore.PushNotificationDelegate? {
    get {
      mock.pushNotificationDelegate.getter.record()
    }
    set {
      mock.pushNotificationDelegate.setter.record(newValue)
    }
  }

  public var registrationDelegate: AirshipCore.RegistrationDelegate? {
    get {
      mock.registrationDelegate.getter.record()
    }
    set {
      mock.registrationDelegate.setter.record(newValue)
    }
  }

  public func updateRegistration() {
    mock.updateRegistration()
  }

  public func resetBadge() {
    mock.resetBadge()
  }
}
