import SwiftUI
import WebKit

final class WebViewManager: ObservableObject {
  let htmlString: String
  var webView: WKWebView
  @Published var occurrences: Int = 0

  init(htmlString: String, webView: WKWebView = WKWebView()) {
    self.htmlString = htmlString
    self.webView = webView
  }

  func searchForText(_ searchText: String) {
    webView.removeAllHighlights()
    if !searchText.isEmpty {
      webView.highlightAllOccurrencesOfString(string: searchText)
      let countCompletionHandler: (Int) -> Void = { [weak self] in
        guard let self else { return }
        if $0 > .zero {
          webView.searchNext() // select first item if there is a match
        }
        self.occurrences = $0
      }
      webView.handleSearchResultCount(completionHandler: countCompletionHandler)
    }
  }

  func searchNextSelector() {
    webView.searchNext()
  }

  func searchPreviousSelector() {
    webView.searchPrevious()
  }
}
