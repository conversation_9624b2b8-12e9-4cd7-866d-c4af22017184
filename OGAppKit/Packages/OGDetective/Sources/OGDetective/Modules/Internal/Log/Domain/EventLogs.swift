import Combine
import OGDIService
import SwiftUI

// MARK: - EventLogsModuleProtocol

protocol EventLogsModuleProtocol: ObservableObject, OGDetectiveModuleProtocol {
  var eventLogsPublisher: Published<[OGEventLog]>.Publisher { get }
}

// MARK: - EventLogsModule

final class EventLogsModule: EventLogsModuleProtocol {
  public var id = UUID()
  public var identifier: UUID { id }
  var exportFileName = "Events"

  @OGInjected(\OGLogsContainer.domainStore) var store

  var eventLogsPublisher: Published<[OGEventLog]>.Publisher { $logs }
  private var cancellables = Set<AnyCancellable>()

  @Published private var logs: [OGEventLog] = .init()

  init() {
    Task {
      await observeLogs()
    }
  }

  private func observeLogs() async {
    await store.watch(keyPath: \.logs)
      .receive(on: DispatchQueue.main)
      .sink { [weak self] logs in
        self?.logs = logs.compactMap { $0 as? OGEventLog }
      }
      .store(in: &cancellables)
  }

  private var snapshots: [Date: [OGEventLog]] = [:]

  func exportData(forDate date: Date) -> String? {
    guard let snapshotRequests = snapshots[date] else { return nil }
    let separator = """

    ---------------------------------------------

    """
    return snapshotRequests.map(\.exportString).joined(separator: separator)
  }

  func didTakeSnapshot(atDate date: Date) {
    snapshots[date] = logs
  }

  func dashboardView() -> some View {
    dashboardViewWrapper()
  }

  @ViewBuilder
  private func dashboardViewWrapper() -> some View {
    EmptyView()
  }

  func snapshotView(forSnapshotDate date: Date) -> some View {
    snapshotViewWrapper(date: date)
  }

  @ViewBuilder
  private func snapshotViewWrapper(date _: Date) -> some View {
    EmptyView()
  }
}

extension OGEventLog {
  static let fallbackString = "NA"

  public var exportString: String {
    """
    Event: \(event.name)
    Properties: \(event.properties)
    Date: \(Date(timeIntervalSince1970: TimeInterval(timestamp / 1_000)))
    Tracking Services: \(trackingServices)
    """
  }
}
