import OGLogger
import SwiftUI

// MARK: - OGSystemLog

extension OGSystemLog {
  func getColor() -> Color {
    guard let level = OGLogLevel(rawValue: tag ?? "") else { return Theme.Token.Colors.text_light }
    switch level {
    case .debug:
      return Theme.Token.Colors.yellow_log
    case .warning:
      return Theme.Token.Colors.orange_log
    case .critical:
      return Theme.Token.Colors.red_log
    }
  }
}

// MARK: - OGEventLog

extension OGEventLog {
  func getColor() -> Color {
    Theme.Token.Colors.blue_log
  }

  func getFormattedProperties() -> String? {
    var message = ""
    let properties = event.properties

    guard !properties.isEmpty else { return nil }

    properties.enumerated().forEach { index, property in
      let (key, value) = property
      let newLine = index != properties.count - 1 ? "\n" : ""

      message += "\(key): \(value)\(newLine)"
    }

    return message
  }
}

// MARK: - OGNetworkLog

extension OGNetworkLog {
  func getColor() -> Color {
    switch httpStatus {
    case 0 ..< 300:
      return Theme.Token.Colors.green_log

    case 300 ..< 400:
      return Theme.Token.Colors.yellow_log

    default:
      return Theme.Token.Colors.red_log
    }
  }
}
