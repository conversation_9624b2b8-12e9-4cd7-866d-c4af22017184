import SwiftUI
import WebKit

// MARK: - WebView

struct WebView: UIViewRepresentable {
  let webViewManager: WebViewManager
  @State private var initialLoad: Bool = true

  init(webViewManager: WebViewManager) {
    self.webViewManager = webViewManager
  }

  func makeUIView(context _: Context) -> WKWebView {
    webViewManager.webView
  }

  func updateUIView(_ webView: WKWebView, context _: Context) {
    if initialLoad {
      DispatchQueue.main.async {
        webView.loadHTMLString(injectedCodeIntoHtml(webViewManager.htmlString), baseURL: nil)
        initialLoad = false
      }
    }
  }

  private func injectedCodeIntoHtml(_ code: String) -> String {
    guard let filePath = Bundle.module.path(forResource: "WebView+HTML", ofType: "html") else {
      return code
    }

    do {
      var htmlContent = try String(contentsOfFile: filePath, encoding: .utf8)
      htmlContent = htmlContent.replacingOccurrences(of: "{{CODE}}", with: code)

      return htmlContent
    } catch {
      return code
    }
  }
}

// MARK: - WebView_Previews

struct WebView_Previews: PreviewProvider {
  static let json = """
  {
    "name": "John Doe",
    "age": 30,
    "city": null
  }
  """

  static var previews: some View {
    WebView(webViewManager: WebViewManager(htmlString: json))
  }
}
