import OGCopyCodeBannerTestsUtils
import OGScreenViewUpdate
import XCTest

@testable import OGCopyCodeBanner

final class OGCopyCodeBannerConnectorTests: XCTestCase {
  func test_WHEN_copyCodeBannerFeature_isEnabled_and_urlMatched_THEN_dispatches_setCurrentUrlMatched_action() async throws {
    CopyCodeBannerFeatureAdapterContainer.shared.copyCodeBanner.register {
      CopyCodeBannerFeatureAdapterMock(isEnabled: true, supportedUrls: ["https://example.com"])
    }

    let screenViewUpdateMock = OGScreenViewUpdateContainer.shared.screenViewUpdate()
    screenViewUpdateMock.send(with: URL(string: "https://example.com"))

    let expectation = expectation(description: "Expected _setCurrentUrlMatched action to be dispatched")

    let sut = OGCopyCodeBannerState.Connector(
      screenViewUpdate: screenViewUpdateMock
    )

    var dispatchedActions: [OGCopyCodeBannerAction] = []
    let dispatch: (OGCopyCodeBannerAction) async -> Void = { action in
      dispatchedActions.append(action)
      if dispatchedActions.count == 1 {
        expectation.fulfill()
      }
    }

    await sut.configure(dispatch: dispatch)

    await fulfillment(of: [expectation], timeout: 1)
    XCTAssertEqual(dispatchedActions, [._setCurrentUrlMatched(true)])
  }

  func test_WHEN_copyCodeBannerFeature_isDisabled_THEN_does_not_dispatch_action() async throws {
    let sut = OGCopyCodeBannerState.Connector()

    var dispatchedActions: [OGCopyCodeBannerAction] = []
    let dispatch: (OGCopyCodeBannerAction) async -> Void = { action in
      dispatchedActions.append(action)
    }

    await sut.configure(dispatch: dispatch)
    XCTAssertTrue(dispatchedActions.isEmpty)
  }
}
