@testable import OGCopyCodeBanner
import XCTest

final class OGCopyCodeBannerReducerTests: XCTestCase {
  func test_WHEN_setDealSeen_action_THEN_isDealSeen_is_true() {
    var state = OGCopyCodeBannerState.initial
    let action = OGCopyCodeBannerAction.setDealSeen(true)

    OGCopyCodeBannerState.Reducer.reduce(&state, with: action)

    XCTAssertTrue(state.isDealSeen)
  }

  func test_WHEN_setCurrentUrlMatched_action_THEN_isCurrentUrlMatched_is_updated() {
    var state = OGCopyCodeBannerState.initial
    let action = OGCopyCodeBannerAction._setCurrentUrlMatched(true)

    OGCopyCodeBannerState.Reducer.reduce(&state, with: action)

    XCTAssertTrue(state.isCurrentUrlMatched)
  }

  func test_GIVEN_isCurrentUrlMatched_and_isDealSeen_TRUE_THEN_shouldShowBanner_is_true() {
    var state = OGCopyCodeBannerState.initial
    state.update(isCurrentUrlMatched: true, isDealSeen: true)

    XCTAssertTrue(state.shouldShowBanner)
  }

  func test_GIVEN_isCurrentUrlMatched_false_THEN_shouldShowBanner_is_false() {
    var state = OGCopyCodeBannerState.initial
    state.update(isCurrentUrlMatched: false, isDealSeen: true)

    XCTAssertFalse(state.shouldShowBanner)
  }
}
