import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGCopyCodeBannerViewStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGCopyCodeBannerView instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGCopyCodeBannerViewStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGCopyCodeBannerView``.
  /// - Returns: A view that represents a OGCopyCodeBannerView.
  @ViewBuilder
  public func register(_ style: some OGCopyCodeBannerViewStyle) -> some View {
    environment(\.styleOGCopyCodeBannerView, AnyOGCopyCodeBannerViewStyle(style: style))
  }
}
