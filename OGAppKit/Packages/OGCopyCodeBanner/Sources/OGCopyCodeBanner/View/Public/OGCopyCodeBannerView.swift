// swiftlint:disable type_name

import SwiftUI

// MARK: - OGCopyCodeBannerView

public struct OGCopyCodeBannerView: View {
  @Environment(\.styleOGCopyCodeBannerView) private var style

  public init() {}

  public var body: some View {
    style.makeBody(configuration: OGCopyCodeBannerViewStyleConfiguration(
      content: OGCopyCodeBannerViewStyleConfiguration.Content(content: content)))
  }

  private var content: some View {
    EmptyView()
  }
}

// MARK: - OGWebViewSalutation_Previews

struct OGWebViewSalutation_Previews: PreviewProvider {
  static var previews: some View {
    OGCopyCodeBannerView()
  }
}
