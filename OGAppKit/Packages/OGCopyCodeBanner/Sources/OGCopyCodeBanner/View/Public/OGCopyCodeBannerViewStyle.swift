import SwiftUI

/// A type that applies custom appearance to
/// all OGCopyCodeBanner within a view hierarchy.
public protocol OGCopyCodeBannerViewStyle {
  /// A view that represents the body of a OGCopyCodeBannerView.
  associatedtype Body: View

  /// The properties of a OGCopyCodeBanner.
  typealias Configuration = OGCopyCodeBannerViewStyleConfiguration

  /// Creates a view that represents the body of a OGCopyCodeBannerView.
  ///
  /// The system calls this method for each OGCopyCodeBannerView instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGCopyCodeBannerView``.
  /// ```swift
  /// struct MyCustomOGCopyCodeBannerStyle: OGCopyCodeBannerViewStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGCopyCodeBannerViewStyleConfiguration`` of the  OGCopyCodeBannerView.
  /// - Returns: A view that represents a OGCopyCodeBannerView.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
