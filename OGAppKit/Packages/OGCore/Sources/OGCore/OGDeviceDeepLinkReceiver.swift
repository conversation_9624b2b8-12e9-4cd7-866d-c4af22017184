import Combine
import Foundation

// MARK: - OGDeviceDeepLinkReceivable

public protocol OGDeviceDeepLinkReceivable {
  var deepLink: CurrentValueSubject<URL?, Never> { get }
  func set(deepLink: URL)
}

// MARK: - OGDeviceDeepLinkReceiver

public final class OGDeviceDeepLinkReceiver: OGDeviceDeepLinkReceivable {
  public private(set) var deepLink: CurrentValueSubject<URL?, Never> = CurrentValueSubject(nil)
  public init() {}
  public func set(deepLink value: URL) {
    deepLink.send(value)
  }
}
