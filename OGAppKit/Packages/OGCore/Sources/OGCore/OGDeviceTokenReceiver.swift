import Combine
import Foundation

// MARK: - OGDeviceTokenReceivable

public protocol OGDeviceTokenReceivable {
  var token: CurrentValueSubject<Data?, Never> { get }
  func set(token: Data)
}

// MARK: - OGDeviceTokenReceiver

public final class OGDeviceTokenReceiver: OGDeviceTokenReceivable {
  public private(set) var token: CurrentValueSubject<Data?, Never> = CurrentValueSubject(nil)
  public init() {}
  public func set(token value: Data) {
    token.send(value)
  }
}
