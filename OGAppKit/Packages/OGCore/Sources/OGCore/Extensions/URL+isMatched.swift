import Foundation

extension URL {
  /// Checks if the URL is matched by a regex
  /// - parameter regex: the regex to match against the URL
  /// - returns: true if the URL is matched by the regex, false otherwise
  public func isMatched(by urlPattern: String) -> Bool {
    if let regEx = NSRegularExpression.from(urlPattern) {
      let matches = regEx.matches(in: absoluteString, options: NSRegularExpression.MatchingOptions(rawValue: 0), range: NSRange(location: 0, length: absoluteString.count))
      return !matches.isEmpty
    }

    return false
  }

  /// Checks if the URL is matched by a regex in the list (prefixed with "regex:"), or is contained by the list
  /// - parameter matcherList: the list of URLs and regexes to use for matching
  /// - returns: true if the URL is matched by the list, false otherwise
  public func isMatched(by urlPatternList: [String]) -> Bool {
    for urlPattern in urlPatternList where isMatched(by: urlPattern) {
      return true
    }

    return false
  }
}
