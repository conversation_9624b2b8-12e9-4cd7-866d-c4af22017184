// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "OGZipArchiver",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGZipArchiver",
      targets: ["OGZipArchiver"]
    )
  ],
  dependencies: [
    .package(path: "../../../OGExternalDependencies")
  ],
  targets: [
    .target(
      name: "OGZipArchiver",
      dependencies: [
        "OGExternalDependencies"
      ]
    ),
    .testTarget(
      name: "OGZipArchiverTests",
      dependencies: ["OGZipArchiver"],
      path: "Tests",
      resources: [
        .process("Fixtures")
      ]
    )
  ]
)
