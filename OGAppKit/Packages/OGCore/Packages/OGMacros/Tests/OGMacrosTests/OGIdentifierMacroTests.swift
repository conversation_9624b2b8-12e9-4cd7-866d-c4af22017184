import OGIdentifier
import OGMacros
import SwiftSyntaxMacros
import SwiftSyntaxMacrosTestSupport
import XCTest

// MARK: - OGIdentifierMacroTests

final class OGIdentifierMacroTests: XCTestCase {
  func test_IdentifierMacror_init_returnsIfDoubleCharacter() throws {
    let sut = #identifier("ab")
    XCTAssertEqual(sut.value, "ab")
  }

  func test_IdentifierMacro_init_returnsIfCombinationOfLetterAndNumber() throws {
    let sut = #identifier("feature1")
    XCTAssertEqual(sut.value, "feature1")
  }
}
