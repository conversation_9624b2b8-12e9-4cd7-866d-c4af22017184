import SwiftSyntax

// MARK: - MockedClassDeclFactory

struct MockedClassDeclFactory {
  func make(typeName: TokenSyntax, from classDecl: ClassDeclSyntax) -> ClassDeclSyntax {
    ClassDeclSyntax(
      leadingTrivia: .init(stringLiteral: "public "),
      name: typeName,
      memberBlockBuilder: {
        let variables = classDecl.memberBlock.members
          .compactMap { $0.decl.as(VariableDeclSyntax.self) }
        MockVariableDeclarationFactory().mockVariableDeclarations(variables)

        let functions = classDecl.memberBlock.members
          .compactMap { $0.decl.as(FunctionDeclSyntax.self) }

        let functionsFactory = FunctionMockableDeclarationFactory()
        functionsFactory.callTrackerDeclarations(functions, nameBuilder: NameBuilder())
        functionsFactory.mockImplementations(for: functions, nameBuilder: NameBuilder())
        functionsFactory.varMockStruct(functions, nameBuilder: NameBuilder())
      }
    )
  }
}

// MARK: - NameBuilder

final class NameBuilder {
  var classNames: [String] = []
  var varNames: [String] = []
  func createClassName(function: FunctionDeclSyntax) -> String {
    var className = "OGMock" + function.name.text.capitalizedSentence + "Calls"
    if classNames.contains(className) {
      let paramName = function.signature.parameterClause.parameters
        .map { parameter in
          let paramName = parameter.firstName.text == "_" ? (parameter.secondName?.text ?? "Parameter") : parameter.firstName.text
          return paramName.trimmingCharacters(in: .whitespacesAndNewlines).capitalizedSentence
        }.joined()
      className = "OGMock" + function.name.text.capitalizedSentence + paramName + "Calls"
    }
    classNames.append(className)
    return className
  }

  func createVarName(function: FunctionDeclSyntax) -> String {
    var varName = function.name.text.trimmingCharacters(in: .whitespacesAndNewlines) + "Calls"
    if varNames.contains(varName) {
      let paramName = function.signature.parameterClause.parameters
        .map { parameter in
          let paramName = parameter.firstName.text == "_" ? (parameter.secondName?.text ?? "Parameter") : parameter.firstName.text
          return paramName.trimmingCharacters(in: .whitespacesAndNewlines).capitalizedSentence
        }.joined()
      varName = function.name.text.trimmingCharacters(in: .whitespacesAndNewlines) + paramName + "Calls"
    }
    varNames.append(varName)
    return varName
  }
}
