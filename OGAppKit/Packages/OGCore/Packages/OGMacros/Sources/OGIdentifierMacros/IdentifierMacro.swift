import SwiftCompilerPlugin
import SwiftSyntax
import SwiftSyntaxBuilder
import SwiftSyntaxMacros

// MARK: - IdentifierMacro

public struct IdentifierMacro: ExpressionMacro {
  public static func expansion(
    of node: some FreestandingMacroExpansionSyntax,
    in _: some MacroExpansionContext
  ) throws
    -> ExprSyntax {
    guard
      let argument = node.argumentList.first?.expression,
      let segments = argument.as(StringLiteralExprSyntax.self)?.segments,
      segments.count == 1,
      case let .stringSegment(literalSegment)? = segments.first
    else {
      throw IdentifierMacroError.requiresStaticStringLiteral
    }

    if literalSegment.content.text.isEmpty {
      throw IdentifierMacroError.empty
    }
    if
      !literalSegment.content.text.allSatisfy({
        $0.isLetter ||
          $0.isNumber ||
          $0.isPunctuation }) {
      throw IdentifierMacroError.malformedIdentifier(identifierValue: "\(argument)")
    }

    return "try! OGIdentifier(\(argument))"
  }
}

// MARK: - IdentifierMacroError

enum IdentifierMacroError: Error, CustomStringConvertible {
  case requiresStaticStringLiteral
  case empty
  case malformedIdentifier(identifierValue: String)

  var description: String {
    switch self {
    case .requiresStaticStringLiteral:
      return "#identifier requires a static string literal."
    case .empty:
      return "#identifier requires the string to be non-empty."
    case let .malformedIdentifier(identifierValue):
      return "#identifier requires that the string contain only letters, numbers and punctuation: \(identifierValue)"
    }
  }
}

// MARK: - OGIdentifierMacroPlugin

@main
struct OGIdentifierMacroPlugin: CompilerPlugin {
  let providingMacros: [Macro.Type] = [
    IdentifierMacro.self
  ]
}
