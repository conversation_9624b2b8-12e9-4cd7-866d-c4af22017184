import Combine
import Foundation

// MARK: - CancellableStorable

public protocol CancellableStorable: AnyObject {
  var cancellables: Set<AnyCancellable> { get set }
}

// MARK: - OGLogReceivable

public protocol OGLogReceivable: CancellableStorable {
  var logDistributer: OGLogDistributable { get }
  func didReceiveLog(_ log: OGLog)
}

extension OGLogReceivable {
  public func receiveAllLogs() {
    logDistributer.logPublisher
      .sink(receiveValue: { [weak self] log in
        self?.didReceiveLog(log)
      })
      .store(in: &cancellables)
  }

  public func receiveCriticalLogs() {
    logDistributer.logPublisher
      .filter { $0.level == .critical }
      .sink(receiveValue: didReceiveLog)
      .store(in: &cancellables)
  }

  public func receiveCriticalAndWarningLogs() {
    logDistributer.logPublisher
      .filter { $0.level == .critical || $0.level == .warning }
      .sink(receiveValue: didReceiveLog)
      .store(in: &cancellables)
  }
}
