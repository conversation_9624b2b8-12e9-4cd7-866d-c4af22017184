// swift-tools-version: 5.9
import PackageDescription

let package = Package(
  name: "OGLogger",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGLogger",
      targets: ["OGLogger"]
    ),
    .library(
      name: "OGLoggerTestsUtils",
      targets: ["OGLoggerTestsUtils"]
    )
  ],
  targets: [
    .target(name: "OGLogger"),
    .target(
      name: "OGLoggerTestsUtils",
      dependencies: ["OGLogger"],
      path: "TestsUtils"
    )
  ]
)
