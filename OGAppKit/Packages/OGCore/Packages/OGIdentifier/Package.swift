// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.
import CompilerPluginSupport
import PackageDescription

let package = Package(
  name: "OGIdentifier",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGIdentifier",
      targets: ["OGIdentifier"]
    ),
    .library(
      name: "OGIdentifierTestsUtils",
      targets: ["OGIdentifierTestsUtils"]
    )
  ],
  targets: [
    .target(
      name: "OGIdentifier"
    ),
    .target(
      name: "OGIdentifierTestsUtils",
      dependencies: ["OGIdentifier"],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGIdentifierTests",
      dependencies: ["OGIdentifier"]
    )
  ]
)
