import Foundation

// MARK: - OGIdentifier

/// A type that represents an identifier.
public struct OGIdentifier: OGStringValueContainable, Codable {
  /// The string value of the identifier.
  public let value: String

  /// Initializes an `OGIdentifier` with a string value.
  ///
  /// - Parameter value: The string value to initialize the identifier with.
  /// - Throws: An error if the string value is invalid.
  /// Throws an error if the following conditions are met:
  /// value is an empty string
  /// value contains a character that is neither a letter, number, nor punctuation
  public init(_ value: String) throws {
    if value.isEmpty || !value.allSatisfy({ $0.isLetter || $0.isNumber || $0.isPunctuation }) {
      throw Self.decodingError(for: value)
    } else {
      self.init(value: value)
    }
  }

  private init(value: String) {
    self.value = value
  }

  enum CodingKeys: CodingKey {
    case identifier
  }

  /// Encodes this object into the given `Encoder`.
  ///
  /// - Parameter encoder: The encoder to use to encode this object.
  /// - Throws: An error if the object cannot be encoded.
  public func encode(to encoder: Encoder) throws {
    var container = encoder.container(keyedBy: CodingKeys.self)
    try container.encode(value, forKey: .identifier)
  }

  /// Initializes an object from a `Decoder`.
  ///
  /// - Parameter decoder: The decoder to use to initialize the object.
  /// - Throws: An error if the object cannot be initialized from the decoder.
  public init(from decoder: Decoder) throws {
    let container = try decoder.singleValueContainer()
    if let value = try? container.decode(String.self), !value.isEmpty {
      self.value = value
    } else {
      let container = try decoder.container(keyedBy: CodingKeys.self)
      self.value = try container.decode(String.self, forKey: .identifier)
    }
  }
}

// MARK: Hashable

extension OGIdentifier: Hashable {
  /// Hashes this object using the given `Hasher`.
  ///
  /// - Parameter hasher: The hasher to use to hash this object.
  public func hash(into hasher: inout Hasher) {
    hasher.combine(value)
  }

  /// Returns a boolean indicating whether the two given identifiers are equal.
  ///
  /// - Parameters:
  ///   - lhs: The first identifier to compare.
  ///   - rhs: The second identifier to compare.
  /// - Returns: `true` if the two identifiers are equal, `false` otherwise.
  public static func == (lhs: Self, rhs: Self) -> Bool {
    lhs.value == rhs.value
  }
}

// MARK: Sendable

extension OGIdentifier: @unchecked Sendable {}

// MARK: Equatable

extension OGIdentifier: Equatable {}
