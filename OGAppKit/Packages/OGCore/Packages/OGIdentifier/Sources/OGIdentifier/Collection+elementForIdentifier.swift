import Foundation

// MARK: - OGIdentifiableError

public enum OGIdentifiableError: Error {
  /// The error that occurs when an element with a given identifier is not available in a collection.
  case elementNotAvailable(forIdentifier: String)
}

extension Collection where Element: OGIdentifierProvidable {
  /// Returns the element in the collection with the given identifier.
  ///
  /// - Parameter identifier: The identifier of the element to return.
  /// - Returns: The element in the collection with the given identifier.
  /// - Throws: `OGIdentifiableError.elementNotAvailable` if an element with the given identifier is not found.
  public func element(for identifier: OGIdentifier) throws -> Element {
    if let element = first(where: { $0.identifier == identifier }) {
      return element
    }
    throw OGIdentifiableError.elementNotAvailable(forIdentifier: identifier.value)
  }
}
