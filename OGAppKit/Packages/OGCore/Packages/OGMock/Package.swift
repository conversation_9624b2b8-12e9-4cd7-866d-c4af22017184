// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.
import CompilerPluginSupport
import PackageDescription

let package = Package(
  name: "<PERSON>GMock",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "<PERSON>GM<PERSON>",
      targets: ["OGMock"]
    )
  ],
  targets: [
    .target(
      name: "<PERSON>GM<PERSON>"
    )
  ]
)
