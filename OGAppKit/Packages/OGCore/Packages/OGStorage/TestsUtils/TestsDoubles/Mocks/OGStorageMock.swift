import OGStorage

public final class KeyValueStorageMock: OGKeyValueStorable {
  public init() {}

  public private(set) var objectForKeyCallCount = 0
  public private(set) var objectForKeyArg: String?
  public func object(forKey key: String) -> Any? {
    objectForKeyCallCount += 1
    objectForKeyArg = key
    return nil
  }

  public private(set) var setValueForKeyCallCount = 0
  public private(set) var setValueForKeyArg: (Any?, String)?
  public func set(_ value: Any?, forKey key: String) {
    setValueForKeyCallCount += 1
    setValueForKeyArg = (value, key)
  }

  public private(set) var removeObjectForKeyCallCount = 0
  public private(set) var removeObjectForKeyArg: String?
  public func removeObject(forKey key: String) {
    removeObjectForKeyCallCount += 1
    removeObjectForKeyArg = key
  }

  public private(set) var boolForKeyCallCount = 0
  public private(set) var boolForKeyArg: String?
  public func bool(forKey key: String) -> Bool {
    boolForKeyCallCount += 1
    boolForKeyArg = key
    return false
  }

  public private(set) var setBoolForKeyCallCount = 0
  public private(set) var setBoolForKeyArg: (Any?, String)?
  public func set(_ value: Bool, forKey key: String) {
    setBoolForKeyCallCount += 1
    setBoolForKeyArg = (value, key)
  }
}
