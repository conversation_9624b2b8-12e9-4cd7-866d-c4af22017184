import OGStorageTestsUtils
import XCTest

@testable import OGStorage

// MARK: - OGPersistenceKey.Test

extension OGPersistenceKey {
  enum Test: String, RawValueable {
    case key
  }
}

extension String {
  static let stub = "stub"
}

// MARK: - OGStorageTests

final class OGStorageTests: XCTestCase {
  func test_OGLocalStorage_WHEN_persistValueForKey_THEN_setValueForKey_called() throws {
    let mockedStore = KeyValueStorageMock()
    let sut = OGStorage(keyValueStorage: mockedStore)
    sut.persist(value: String.stub, forKey: OGPersistenceKey.Test.key)
    XCTAssertEqual(String.stub, mockedStore.setValueForKeyArg?.0 as? String)
    XCTAssertEqual(OGPersistenceKey.Test.key.rawValue, mockedStore.setValueForKeyArg?.1)
    XCTAssertEqual(1, mockedStore.setValueForKeyCallCount)
  }

  func test_OGLocalStorage_WHEN_valueForKey_THEN_objectForKey_called() throws {
    let mockedStore = KeyValueStorageMock()
    let sut = OGStorage(keyValueStorage: mockedStore)
    let _: String? = sut.value(forKey: OGPersistenceKey.Test.key)
    XCTAssertEqual(OGPersistenceKey.Test.key.rawValue, mockedStore.objectForKeyArg)
    XCTAssertEqual(1, mockedStore.objectForKeyCallCount)
  }

  func test_OGLocalStorage_WHEN_deleteValueForKey_THEN_removeObjectForKey_called() throws {
    let mockedStore = KeyValueStorageMock()
    let sut = OGStorage(keyValueStorage: mockedStore)
    sut.delete(valueForKey: OGPersistenceKey.Test.key)
    XCTAssertEqual(OGPersistenceKey.Test.key.rawValue, mockedStore.removeObjectForKeyArg)
    XCTAssertEqual(1, mockedStore.removeObjectForKeyCallCount)
  }

  func test_OGLocalStorage_WHEN_persistBoolForKey_THEN_setValueForKey_called() throws {
    let mockedStore = KeyValueStorageMock()
    let sut = OGStorage(keyValueStorage: mockedStore)
    sut.persist(value: true, forKey: OGPersistenceKey.Test.key)
    XCTAssertEqual(true, mockedStore.setValueForKeyArg?.0 as? Bool)
    XCTAssertEqual(OGPersistenceKey.Test.key.rawValue, mockedStore.setValueForKeyArg?.1)
    XCTAssertEqual(1, mockedStore.setValueForKeyCallCount)
  }

  func test_OGLocalStorage_WHEN_boolForKey_THEN_objectForKey_called() throws {
    let mockedStore = KeyValueStorageMock()
    let sut = OGStorage(keyValueStorage: mockedStore)
    let _ = sut.bool(forKey: OGPersistenceKey.Test.key)
    XCTAssertEqual(OGPersistenceKey.Test.key.rawValue, mockedStore.objectForKeyArg)
    XCTAssertEqual(1, mockedStore.objectForKeyCallCount)
  }
}
