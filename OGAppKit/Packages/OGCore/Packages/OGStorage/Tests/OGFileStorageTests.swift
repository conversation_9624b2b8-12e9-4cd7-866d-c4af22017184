import OGStorageTestsUtils
import XCTest

@testable import OGStorage

final class OGFileStorageTests: XCTestCase {
  override func tearDown() async throws {
    try? FileManager.default.removeItem(at: Self.folderUrl)
    try await super.tearDown()
  }

  private static var folderUrl: URL {
    FileManager.default.temporaryDirectory
  }

  private static let fileName = "test"

  private static var fileUrl: URL {
    let fileUrl = URL(
      fileURLWithPath: Self.folderUrl
        .appendingPathComponent(Self.fileName)
        .absoluteString
    )
    return fileUrl
  }

  func test_save_createsFile() async throws {
    let fileManager = FileManagerMock()
    let sut = OGFileStorage(fileManager: fileManager)

    let data = DataMock()
    let folderUrl = FileManager.default.temporaryDirectory
    let fileName = "test"

    try await sut.save(
      data,
      folderUrl: folderUrl,
      fileName: fileName
    )

    XCTAssertEqual(1, fileManager.mock.createDirectoryCalls.callsCount)
    XCTAssertEqual(
      folderUrl.absoluteString,
      fileManager.mock.createDirectoryCalls.latestCall?.0
    )

    XCTAssertEqual(1, data.mock.writeCalls.callsCount)
    XCTAssertEqual(
      Self.fileUrl,
      data.mock.writeCalls.latestCall?.0
    )
  }

  func test_delete_deletesFile() async throws {
    let fileManager = FileManagerMock()
    fileManager.mock.fileExistsCalls.mockCall { _ in
      true
    }
    let sut = OGFileStorage(fileManager: fileManager)

    try await sut.delete(url: Self.fileUrl)

    XCTAssertEqual(1, fileManager.mock.fileExistsCalls.callsCount)
    XCTAssertEqual(1, fileManager.mock.removeItemCalls.callsCount)
    XCTAssertEqual(
      Self.fileUrl.path,
      fileManager.mock.fileExistsCalls.latestCall
    )
    XCTAssertEqual(
      Self.fileUrl.path,
      fileManager.mock.removeItemCalls.latestCall
    )
  }
}
