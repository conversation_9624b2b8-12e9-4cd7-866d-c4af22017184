import Foundation

// MARK: - OGPersistenceKey

public enum OGPersistenceKey {}

// MARK: - OGPersistable

/// A protocol that defines methods for storing and retrieving values persistently.
public protocol OGPersistable: OGBoolPersistable {
  /// The type of value being persisted.
  associatedtype T

  /// Persists a value for the given key.
  ///
  /// - Parameters:
  ///   - value: The value to persist.
  ///   - key: The key to use for storing the value.
  func persist(value: T, forKey key: RawValueable)

  /// Returns the value associated with the given key, if it exists.
  ///
  /// - Parameters:
  ///   - key: The key for which to retrieve the associated value.
  /// - Returns: The value associated with the given key, or `nil` if no value is associated with the key.
  func value(forKey key: RawValueable) -> T?

  /// Removes the value associated with the given key from the store.
  ///
  /// - Parameters:
  ///   - key: The key whose value should be removed from the store.
  func delete(valueForKey key: RawValueable)
}

// MARK: - OGBoolPersistable

/// A protocol that defines methods for storing and retrieving boolean values persistently.
public protocol OGBoolPersistable {
  func bool(forKey key: RawValueable) -> Bool
  func persistBool(_ value: Bool, forKey key: RawValueable)
}
