import OGAppEnvironment

public struct AppEnvironmentStub: OGAppEnvironmental {
  public var bundleName: String? = "MOC"
  public var version: String? = "1.0.0"
  public var buildNumber: String? = "100"
  public var appName: String? = "MockApp"
  public var appId: String? = "com.mock.app"
  public var advertisingIdentifier: String? = "unique-id-1234"
  public var systemVersion: String = "iOS 15.0"
  public var systemName: String = "iOS"
  public var deviceModel: String = "iPhone Mock"
  public var isDebugOrBetaBuild: Bool
  public var isDebugBuild: Bool
  public var isTestsBuild: Bool { true }

  public init(isDebugOrBetaBuild: Bool, isDebugBuild: Bool) {
    self.isDebugOrBetaBuild = isDebugOrBetaBuild
    self.isDebugBuild = isDebugBuild
  }
}
