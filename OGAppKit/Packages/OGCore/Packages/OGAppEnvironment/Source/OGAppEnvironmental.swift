import Foundation

/// A protocol that defines properties and methods for accessing information about the environment in which an app is running.
public protocol OGAppEnvironmental {
  /// The name of the bundle
  var bundleName: String? { get }
  /// The version of the app.
  var version: String? { get }
  /// The build number of the app.
  var buildNumber: String? { get }
  /// The name of the app.
  var appName: String? { get }
  /// The app store ID  of the app.
  var appId: String? { get }
  /// The UUID that is specific to a device
  var advertisingIdentifier: String? { get }
  /// The current version of the operating system.
  var systemVersion: String { get }
  /// The name of the operating system running on the device.
  var systemName: String { get }
  /// The current device model
  var deviceModel: String { get }
  /// A boolean value indicating whether the app is a debug or beta build.
  var isDebugOrBetaBuild: Bool { get }
  /// A boolean value indicating a debug  build.
  var isDebugBuild: Bool { get }
  /// A boolean value indicating a tests build.
  var isTestsBuild: Bool { get }

  init(isDebugOrBetaBuild: Bool, isDebugBuild: Bool)
}
