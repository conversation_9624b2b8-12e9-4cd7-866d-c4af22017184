import AdSupport
import AppTrackingTransparency
import Foundation
import UIKit.UIDevice

/// Defines the environment properties of an app.
public struct OGAppEnvironment: OGAppEnvironmental {
  /// A boolean value indicating whether the app is a debug or beta build.
  public private(set) var isDebugOrBetaBuild: Bool

  /// A boolean value indicating a debug build.
  public private(set) var isDebugBuild: Bool

  public init(isDebugOrBetaBuild: Bool, isDebugBuild: Bool) {
    self.isDebugOrBetaBuild = isDebugOrBetaBuild
    self.isDebugBuild = isDebugBuild
  }

  /// A boolean value indicating a tests build.
  public var isTestsBuild: Bool {
    ProcessInfo.processInfo.environment["XCTestConfigurationFilePath"] != nil
  }

  /// The name of the bundle
  public var bundleName: String? {
    Bundle.main.object(forInfoDictionaryKey: "CFBundleName") as? String
  }

  /// The version of the app.
  public var version: String? {
    Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String
  }

  /// The build number of the app.
  public var buildNumber: String? {
    Bundle.main.object(forInfoDictionaryKey: "CFBundleVersion") as? String
  }

  /// The name of the app.
  public var appName: String? {
    Bundle.main.infoDictionary?["CFBundleDisplayName"] as? String ?? Bundle.main.infoDictionary?["CFBundleName"] as? String
  }

  /// The app store ID  of the app.
  public var appId: String? {
    Bundle.main.infoDictionary?["AppStoreID"] as? String
  }

  /// The UUID that is specific to a device
  public var advertisingIdentifier: String? {
    ATTrackingManager.trackingAuthorizationStatus == .authorized ? ASIdentifierManager.shared().advertisingIdentifier.uuidString : nil
  }

  /// The current version of the operating system.
  public var systemVersion: String {
    UIDevice.current.systemVersion
  }

  /// The name of the operating system running on the device.
  public var systemName: String {
    UIDevice.current.systemName
  }

  /// The current device model
  public var deviceModel: String {
    var systemInfo = utsname()
    uname(&systemInfo)
    let machineMirror = Mirror(reflecting: systemInfo.machine)
    let deviceModel = machineMirror.children.reduce("") { identifier, element in
      guard let value = element.value as? Int8, value != 0 else { return identifier }
      return identifier + String(UnicodeScalar(UInt8(value)))
    }
    return deviceModel
  }
}
